import { useTranslation, type Language } from "@/lib/i18n";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, AlertCircle, XCircle, Clock, ArrowLeft } from "lucide-react";
import { Link } from "wouter";

interface StatusProps {
  language: Language;
}

export default function Status({ language }: StatusProps) {
  const { t } = useTranslation(language);

  const services = [
    {
      name: t("status.api_service"),
      status: "operational",
      uptime: "99.9%",
      responseTime: "120ms"
    },
    {
      name: t("status.web_service"),
      status: "operational", 
      uptime: "99.8%",
      responseTime: "45ms"
    },
    {
      name: t("status.image_processing"),
      status: "operational",
      uptime: "99.7%",
      responseTime: "2.3s"
    },
    {
      name: t("status.file_storage"),
      status: "operational",
      uptime: "99.9%",
      responseTime: "80ms"
    }
  ];

  const incidents = [
    {
      date: "2025-06-28",
      title: t("status.incident_1_title"),
      description: t("status.incident_1_desc"),
      status: "resolved",
      duration: "23 minutes"
    },
    {
      date: "2025-06-25", 
      title: t("status.incident_2_title"),
      description: t("status.incident_2_desc"),
      status: "resolved",
      duration: "1 hour 15 minutes"
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "operational":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "degraded":
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case "outage":
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "operational":
        return <Badge className="bg-green-100 text-green-800">{t("status.operational")}</Badge>;
      case "degraded":
        return <Badge className="bg-yellow-100 text-yellow-800">{t("status.degraded")}</Badge>;
      case "outage":
        return <Badge className="bg-red-100 text-red-800">{t("status.outage")}</Badge>;
      case "resolved":
        return <Badge className="bg-gray-100 text-gray-800">{t("status.resolved")}</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              返回首页
            </Button>
          </Link>
        </div>
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            {t("status.title")}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t("status.description")}
          </p>
        </div>

        {/* Overall Status */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-center justify-center space-x-3">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <span className="text-2xl font-semibold text-green-600">
                {t("status.all_systems_operational")}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Service Status */}
        <div className="grid gap-6 mb-16">
          <Card>
            <CardHeader>
              <CardTitle>{t("status.service_status")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {services.map((service, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(service.status)}
                      <span className="font-medium">{service.name}</span>
                    </div>
                    <div className="flex items-center space-x-6 text-sm text-gray-600">
                      <span>{t("status.uptime")}: {service.uptime}</span>
                      <span>{t("status.response_time")}: {service.responseTime}</span>
                      {getStatusBadge(service.status)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Incidents */}
        <Card>
          <CardHeader>
            <CardTitle>{t("status.recent_incidents")}</CardTitle>
          </CardHeader>
          <CardContent>
            {incidents.length > 0 ? (
              <div className="space-y-6">
                {incidents.map((incident, index) => (
                  <div key={index} className="border-l-4 border-gray-200 pl-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">{incident.title}</h3>
                      {getStatusBadge(incident.status)}
                    </div>
                    <p className="text-gray-600 mb-2">{incident.description}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>{incident.date}</span>
                      <span>{t("status.duration")}: {incident.duration}</span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-600">{t("status.no_incidents")}</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}