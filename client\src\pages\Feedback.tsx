import { useTranslation, type Language } from "@/lib/i18n";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MessageSquare, Lightbulb, Bug, Star, ArrowLeft } from "lucide-react";
import { Link } from "wouter";

interface FeedbackProps {
  language: Language;
}

export default function Feedback({ language }: FeedbackProps) {
  const { t } = useTranslation(language);

  const feedbackTypes = [
    {
      value: "bug",
      label: t("feedback.type_bug"),
      icon: Bug,
      description: t("feedback.type_bug_desc")
    },
    {
      value: "feature",
      label: t("feedback.type_feature"),
      icon: Lightbulb,
      description: t("feedback.type_feature_desc")
    },
    {
      value: "general",
      label: t("feedback.type_general"),
      icon: MessageSquare,
      description: t("feedback.type_general_desc")
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              返回首页
            </Button>
          </Link>
        </div>
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            {t("feedback.title")}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t("feedback.description")}
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Feedback Types */}
          <div className="lg:col-span-1">
            <h2 className="text-xl font-semibold mb-6">{t("feedback.types_title")}</h2>
            <div className="space-y-4">
              {feedbackTypes.map((type) => {
                const IconComponent = type.icon;
                return (
                  <Card key={type.value} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-3">
                        <div className="gradient-bg rounded-lg p-2 flex-shrink-0">
                          <IconComponent className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-medium mb-1">{type.label}</h3>
                          <p className="text-sm text-gray-600">{type.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Feedback Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Star className="h-6 w-6 text-purple-600" />
                  <span>{t("feedback.form_title")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t("feedback.name")}
                    </label>
                    <Input placeholder={t("feedback.name_placeholder")} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t("feedback.email")}
                    </label>
                    <Input type="email" placeholder={t("feedback.email_placeholder")} />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t("feedback.type")}
                  </label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder={t("feedback.type_placeholder")} />
                    </SelectTrigger>
                    <SelectContent>
                      {feedbackTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t("feedback.subject")}
                  </label>
                  <Input placeholder={t("feedback.subject_placeholder")} />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t("feedback.message")}
                  </label>
                  <Textarea 
                    placeholder={t("feedback.message_placeholder")}
                    className="min-h-[150px]"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t("feedback.rating")}
                  </label>
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <button
                        key={rating}
                        className="p-1 hover:text-yellow-400 transition-colors"
                      >
                        <Star className="h-6 w-6 text-gray-300 hover:text-yellow-400" />
                      </button>
                    ))}
                  </div>
                </div>

                <Button className="w-full gradient-bg text-white">
                  {t("feedback.submit")}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}