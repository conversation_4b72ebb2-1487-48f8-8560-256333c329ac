import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useTranslation, type Language } from "@/lib/i18n";
import { SEOHead } from "@/components/SEOHead";
import { 
  Code2, 
  Smartphone, 
  Monitor, 
  Globe, 
  Download, 
  Zap,
  ArrowRight,
  CheckCircle,
  ExternalLink,
  ArrowLeft
} from "lucide-react";
import { Link } from "wouter";

interface ToolsProps {
  language: Language;
}

export default function Tools({ language }: ToolsProps) {
  const { t } = useTranslation(language);

  const apiFeatures = [
    { icon: <Zap className="h-5 w-5" />, title: "Lightning Fast", description: "Process images in under 5 seconds" },
    { icon: <CheckCircle className="h-5 w-5" />, title: "99.9% Uptime", description: "Reliable service you can count on" },
    { icon: <Globe className="h-5 w-5" />, title: "Global CDN", description: "Fast delivery worldwide" },
  ];

  const integrations = [
    {
      title: "Photoshop Plugin",
      description: "Remove backgrounds directly in Adobe Photoshop",
      icon: <Monitor className="h-8 w-8" />,
      status: "Available",
      link: "#",
    },
    {
      title: "Figma Plugin",
      description: "Seamlessly integrate into your design workflow",
      icon: <Monitor className="h-8 w-8" />,
      status: "Available",
      link: "#",
    },
    {
      title: "Canva Integration",
      description: "Use remove.bg directly in Canva",
      icon: <Globe className="h-8 w-8" />,
      status: "Available",
      link: "#",
    },
    {
      title: "Shopify App",
      description: "Optimize product images for your store",
      icon: <Smartphone className="h-8 w-8" />,
      status: "Available",
      link: "#",
    },
    {
      title: "WordPress Plugin",
      description: "Automatically optimize images on upload",
      icon: <Globe className="h-8 w-8" />,
      status: "Available",
      link: "#",
    },
    {
      title: "Mobile Apps",
      description: "iOS and Android apps available",
      icon: <Smartphone className="h-8 w-8" />,
      status: "Available",
      link: "#",
    },
  ];

  const codeExample = `// Remove background using our API
const formData = new FormData();
formData.append('image_file', imageFile);

const response = await fetch('https://api.remove.bg/v1.0/removebg', {
  method: 'POST',
  headers: {
    'X-Api-Key': 'YOUR_API_KEY',
  },
  body: formData
});

const result = await response.blob();`;

  return (
    <>
      <SEOHead
        title="Tools & API - Remove.bg"
        description="Integrate powerful background removal into your applications with our API, plugins, and tools. Available for Photoshop, Figma, Shopify, and more."
        language={language}
      />

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Back Button */}
            <div className="mb-8">
              <Link href="/">
                <Button variant="ghost" className="gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  返回首页
                </Button>
              </Link>
            </div>
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-6xl font-bold mb-6 gradient-text">
                Tools & API
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Integrate powerful background removal into your workflow with our comprehensive suite of tools, plugins, and API.
              </p>
            </div>

            {/* API Features */}
            <div className="grid md:grid-cols-3 gap-8 mb-16">
              {apiFeatures.map((feature, index) => (
                <div key={index} className="text-center">
                  <div className="gradient-bg rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 text-white">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>

            <div className="text-center">
              <Button className="gradient-bg text-white font-semibold px-8 py-4 rounded-xl hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1 mr-4">
                <Code2 className="h-4 w-4 mr-2" />
                View API Documentation
              </Button>
              <Button variant="outline" className="border-2 border-purple-600 text-purple-600 font-semibold px-8 py-4 rounded-xl hover:bg-purple-600 hover:text-white transition-all duration-200">
                Get API Key
              </Button>
            </div>
          </div>
        </section>

        {/* API Section */}
        <section className="py-16 lg:py-24">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">
                  Powerful API for Developers
                </h2>
                <p className="text-xl text-gray-600 mb-8">
                  Integrate background removal into your applications with just a few lines of code. Our RESTful API is fast, reliable, and easy to use.
                </p>
                
                <div className="space-y-4 mb-8">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>Process up to 10MB images</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>Support for JPG, PNG, WebP formats</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>High-resolution output up to 25 megapixels</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>99.9% uptime SLA</span>
                  </div>
                </div>

                <Button className="gradient-bg text-white font-semibold px-8 py-4 rounded-xl hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1">
                  Start Free Trial
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>

              <div>
                <Card className="shadow-xl">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">API Example</CardTitle>
                      <Badge variant="secondary">JavaScript</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <pre className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                      <code>{codeExample}</code>
                    </pre>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Integrations Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Integrations & Plugins
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Seamlessly integrate remove.bg into your favorite tools and platforms. No coding required.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {integrations.map((integration, index) => (
                <Card key={index} className="shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-4">
                      <div className="gradient-bg rounded-lg p-3 text-white">
                        {integration.icon}
                      </div>
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        {integration.status}
                      </Badge>
                    </div>
                    <CardTitle className="text-xl">{integration.title}</CardTitle>
                    <CardDescription className="text-gray-600">
                      {integration.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button variant="outline" className="w-full" asChild>
                      <a href={integration.link}>
                        <Download className="h-4 w-4 mr-2" />
                        Install
                        <ExternalLink className="h-4 w-4 ml-2" />
                      </a>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing CTA */}
        <section className="py-16 lg:py-24 bg-gray-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to get started?
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Choose the plan that fits your needs. Start with our free tier or scale up for high-volume processing.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="gradient-bg text-white font-semibold px-8 py-4 rounded-xl hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1">
                View Pricing
              </Button>
              <Button variant="outline" className="border-2 border-white text-white font-semibold px-8 py-4 rounded-xl hover:bg-white hover:text-gray-900 transition-all duration-200">
                Contact Sales
              </Button>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
