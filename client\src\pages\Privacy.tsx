import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTranslation, type Language } from "@/lib/i18n";
import { SEOHead } from "@/components/SEOHead";
import { Shield, Eye, Lock, UserCheck, FileText, Mail, ArrowLeft } from "lucide-react";
import { Link } from "wouter";

interface PrivacyProps {
  language: Language;
}

export default function Privacy({ language }: PrivacyProps) {
  const { t } = useTranslation(language);

  const sections = [
    {
      id: "information-collection",
      title: "Information We Collect",
      icon: <Eye className="h-6 w-6" />,
      content: `We collect information you provide directly to us, such as when you create an account, upload images, subscribe to our newsletter, or contact us for support.

This includes:
• Email address and account information
• Images you upload for processing
• Payment information (processed securely by third-party providers)
• Communications with our support team
• Usage data and analytics to improve our service`
    },
    {
      id: "information-use",
      title: "How We Use Your Information",
      icon: <UserCheck className="h-6 w-6" />,
      content: `We use the information we collect to:
• Provide and improve our background removal service
• Process your images using our AI technology
• Send you service updates and marketing communications (with consent)
• Provide customer support
• Analyze usage patterns to enhance user experience
• Comply with legal obligations and prevent fraud`
    },
    {
      id: "data-security",
      title: "Data Security",
      icon: <Lock className="h-6 w-6" />,
      content: `We implement industry-standard security measures to protect your data:
• SSL/TLS encryption for data transmission
• Secure cloud storage with encryption at rest
• Regular security audits and monitoring
• Access controls and authentication systems
• Automatic image deletion after processing (unless you choose to save)

Your uploaded images are automatically deleted from our servers after 24 hours unless you explicitly save them to your account.`
    },
    {
      id: "data-sharing",
      title: "Information Sharing",
      icon: <Shield className="h-6 w-6" />,
      content: `We do not sell, trade, or rent your personal information to third parties. We may share your information only in these limited circumstances:

• With service providers who help us operate our business (under strict confidentiality agreements)
• When required by law or to respond to legal process
• To protect our rights, safety, or property
• With your explicit consent
• In connection with a business transfer or acquisition`
    },
    {
      id: "your-rights",
      title: "Your Rights and Choices",
      icon: <FileText className="h-6 w-6" />,
      content: `You have several rights regarding your personal information:

• Access: Request a copy of the personal information we hold about you
• Correction: Request correction of inaccurate or incomplete information
• Deletion: Request deletion of your personal information
• Portability: Request transfer of your data to another service
• Opt-out: Unsubscribe from marketing communications at any time
• Withdraw consent: Withdraw consent for data processing where applicable

To exercise these rights, please contact <NAME_EMAIL>.`
    },
    {
      id: "contact",
      title: "Contact Information",
      icon: <Mail className="h-6 w-6" />,
      content: `If you have questions about this Privacy Policy or our data practices, please contact us:

Email: <EMAIL>
Address: Remove.bg Privacy Team
123 AI Technology Street
San Francisco, CA 94105
United States

We will respond to your inquiry within 30 days.`
    }
  ];

  return (
    <>
      <SEOHead
        title="Privacy Policy - Remove.bg"
        description="Learn how Remove.bg protects your privacy and handles your personal data. Comprehensive privacy policy covering data collection, usage, and your rights."
        language={language}
      />

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Back Button */}
            <div className="mb-8">
              <Link href="/">
                <Button variant="ghost" className="gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  返回首页
                </Button>
              </Link>
            </div>
            <div className="text-center mb-16">
              <div className="gradient-bg rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Shield className="h-10 w-10 text-white" />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                Privacy Policy
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Your privacy is important to us. This policy explains how we collect, use, and protect your personal information.
              </p>
              <div className="mt-6 text-sm text-gray-500">
                <p>Last updated: December 29, 2024</p>
                <p>Effective date: January 1, 2025</p>
              </div>
            </div>
          </div>
        </section>

        {/* Privacy Sections */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Quick Overview */}
            <Card className="mb-12 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl gradient-text">Quick Overview</CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600 space-y-4">
                <p>
                  <strong>TL;DR:</strong> We respect your privacy and are committed to protecting your personal data. 
                  Here's what you need to know:
                </p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>We only collect data necessary to provide our service</li>
                  <li>Your uploaded images are automatically deleted after 24 hours</li>
                  <li>We don't sell your personal information to third parties</li>
                  <li>You have full control over your data and can request deletion at any time</li>
                  <li>We use industry-standard security measures to protect your information</li>
                </ul>
              </CardContent>
            </Card>

            {/* Detailed Sections */}
            <div className="space-y-8">
              {sections.map((section, index) => (
                <Card key={section.id} className="shadow-lg">
                  <CardHeader>
                    <div className="flex items-center mb-4">
                      <div className="gradient-bg rounded-lg p-3 text-white mr-4">
                        {section.icon}
                      </div>
                      <CardTitle className="text-xl text-gray-900">
                        {index + 1}. {section.title}
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-gray-600 whitespace-pre-line leading-relaxed">
                      {section.content}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* GDPR Notice */}
            <Card className="mt-12 shadow-lg border-l-4 border-blue-500">
              <CardHeader>
                <CardTitle className="text-xl text-blue-700">
                  GDPR Compliance Notice
                </CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600">
                <p>
                  For users in the European Union, we comply with the General Data Protection Regulation (GDPR). 
                  You have additional rights under GDPR, including the right to data portability and the right to 
                  lodge a complaint with a supervisory authority.
                </p>
                <p className="mt-4">
                  Our lawful basis for processing your data is typically:
                </p>
                <ul className="list-disc pl-6 mt-2 space-y-1">
                  <li>Consent: When you voluntarily provide information</li>
                  <li>Contract: To provide our services</li>
                  <li>Legitimate interests: To improve our service and prevent fraud</li>
                </ul>
              </CardContent>
            </Card>

            {/* California Privacy Rights */}
            <Card className="mt-8 shadow-lg border-l-4 border-purple-500">
              <CardHeader>
                <CardTitle className="text-xl text-purple-700">
                  California Privacy Rights (CCPA)
                </CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600">
                <p>
                  California residents have specific rights under the California Consumer Privacy Act (CCPA):
                </p>
                <ul className="list-disc pl-6 mt-2 space-y-1">
                  <li>Right to know what personal information is collected</li>
                  <li>Right to delete personal information</li>
                  <li>Right to opt-out of the sale of personal information</li>
                  <li>Right to non-discrimination for exercising privacy rights</li>
                </ul>
                <p className="mt-4">
                  <strong>Note:</strong> We do not sell personal information as defined by the CCPA.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </>
  );
}
