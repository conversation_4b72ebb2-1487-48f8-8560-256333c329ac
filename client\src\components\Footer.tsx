import { <PERSON> } from "wouter";
import { useTranslation, type Language } from "@/lib/i18n";
import { Scissors, Facebook, Twitter, Instagram, Linkedin } from "lucide-react";

interface FooterProps {
  language: Language;
}

export function Footer({ language }: FooterProps) {
  const { t } = useTranslation(language);

  const footerLinks = {
    support: [
      { name: t("footer.support.help"), href: "/help" },
      { name: t("footer.support.contact"), href: "/contact" },
      { name: t("footer.support.status"), href: "/status" },
      { name: t("footer.support.feedback"), href: "/feedback" },
    ],
    legal: [
      { name: t("footer.legal.terms"), href: "/terms" },
      { name: t("footer.legal.privacy"), href: "/privacy" },
      { name: t("footer.legal.cookies"), href: "/cookies" },
      { name: t("footer.legal.imprint"), href: "/imprint" },
    ],
  };

  const socialLinks = [
    { Icon: Facebook, href: "https://facebook.com/removebg", label: "Facebook" },
    { Icon: Twitter, href: "https://twitter.com/removebg", label: "Twitter" },
    { Icon: Instagram, href: "https://instagram.com/removebg", label: "Instagram" },
    { Icon: Linkedin, href: "https://linkedin.com/company/removebg", label: "LinkedIn" },
  ];

  return (
    <footer className="bg-gray-900 text-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Brand Column */}
          <div className="lg:col-span-1">
            <div className="flex items-center mb-6">
              <div className="gradient-bg rounded-lg p-2 mr-3">
                <Scissors className="h-5 w-5 text-white" />
              </div>
              <span className="text-2xl font-bold">Remove.bg</span>
            </div>
            <p className="text-gray-400 mb-6">
              {t("footer.description")}
            </p>
            <div className="flex space-x-4">
              {socialLinks.map(({ Icon, href, label }) => (
                <a
                  key={label}
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors"
                  aria-label={label}
                >
                  <Icon className="h-5 w-5" />
                </a>
              ))}
            </div>
          </div>

          {/* Support Column */}
          <div>
            <h3 className="text-lg font-semibold mb-6">{t("footer.support.title")}</h3>
            <ul className="space-y-3 text-gray-400">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="hover:text-white transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Column */}
          <div>
            <h3 className="text-lg font-semibold mb-6">{t("footer.legal.title")}</h3>
            <ul className="space-y-3 text-gray-400">
              {footerLinks.legal.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="hover:text-white transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
          <p>{t("footer.copyright")}</p>
        </div>
      </div>
    </footer>
  );
}
