import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { type Language } from "@/lib/i18n";

interface LanguageSwitcherProps {
  language: Language;
  onLanguageChange: (language: Language) => void;
}

export function LanguageSwitcher({ language, onLanguageChange }: LanguageSwitcherProps) {
  const languages = [
    { code: 'en', name: '🇺🇸 EN', label: 'English' },
    { code: 'zh', name: '🇨🇳 中文', label: '中文' },
    { code: 'es', name: '🇪🇸 ES', label: 'Español' },
    { code: 'fr', name: '🇫🇷 FR', label: 'Français' },
    { code: 'de', name: '🇩🇪 DE', label: 'Deutsch' },
  ];

  return (
    <Select value={language} onValueChange={(value) => onLanguageChange(value as Language)}>
      <SelectTrigger className="w-[100px] border-gray-300">
        <SelectValue placeholder="Language" />
      </SelectTrigger>
      <SelectContent>
        {languages.map((lang) => (
          <SelectItem key={lang.code} value={lang.code}>
            {lang.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
