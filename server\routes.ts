import type { Express } from "express";
import { createServer, type Server } from "http";
import multer from "multer";
import path from "path";
import fs from "fs";
import express from "express";
import { storage } from "./storage";
import { insertProcessedImageSchema, insertNewsletterSchema } from "@shared/schema";
import { z } from "zod";

const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

// Function to process image with remove.bg API
async function processImageWithRemoveBg(imageId: number, originalPath: string, filename: string) {
  try {
    const apiKey = process.env.REMOVE_BG_API_KEY;
    if (!apiKey) {
      throw new Error('Remove.bg API key not configured');
    }

    const FormData = await import('form-data');
    const form = new FormData.default();
    
    form.append('image_file', fs.createReadStream(originalPath));
    form.append('size', 'auto');

    const options = {
      method: 'POST',
      hostname: 'api.remove.bg',
      path: '/v1.0/removebg',
      headers: {
        'X-Api-Key': apiKey,
        ...form.getHeaders(),
      },
    };

    const https = await import('https');
    const req = https.request(options, (res) => {
      const processedPath = `processed_${filename}`;
      const outputPath = path.join('uploads', processedPath);
      
      if (res.statusCode === 200) {
        const fileStream = fs.createWriteStream(outputPath);
        res.pipe(fileStream);
        
        fileStream.on('finish', () => {
          storage.updateProcessedImageStatus(imageId, 'completed', processedPath);
        });
        
        fileStream.on('error', (error) => {
          console.error('File write error:', error);
          storage.updateProcessedImageStatus(imageId, 'failed');
        });
      } else {
        console.error('Remove.bg API error:', res.statusCode);
        storage.updateProcessedImageStatus(imageId, 'failed');
      }
    });

    req.on('error', (error) => {
      console.error('Request error:', error);
      storage.updateProcessedImageStatus(imageId, 'failed');
    });

    form.pipe(req);
  } catch (error) {
    console.error('Processing error:', error);
    storage.updateProcessedImageStatus(imageId, 'failed');
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Ensure uploads directory exists
  if (!fs.existsSync('uploads')) {
    fs.mkdirSync('uploads', { recursive: true });
  }

  // Serve uploaded files with proper headers
  app.use('/uploads', express.static('uploads', {
    setHeaders: (res, filepath) => {
      if (filepath.match(/\.(jpg|jpeg|png|gif|webp)$/i) || filepath.includes('processed_')) {
        const ext = path.extname(filepath).toLowerCase();
        if (ext === '.png') {
          res.setHeader('Content-Type', 'image/png');
        } else if (ext === '.gif') {
          res.setHeader('Content-Type', 'image/gif');
        } else if (ext === '.webp') {
          res.setHeader('Content-Type', 'image/webp');
        } else {
          res.setHeader('Content-Type', 'image/jpeg');
        }
      }
    }
  }));

  // Upload and process image
  app.post('/api/upload', upload.single('image'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'No image file provided' });
      }

      const { originalname, filename, path: filepath } = req.file;
      
      // Create processed image record
      const imageData = {
        userId: null, // Anonymous upload
        originalFilename: originalname,
        originalPath: filepath,
        processedPath: '', // Will be updated after processing
        processingStatus: 'pending',
      };

      const processedImage = await storage.createProcessedImage(imageData);

      // Process the image with remove.bg API
      processImageWithRemoveBg(processedImage.id, filepath, filename);

      res.json({
        id: processedImage.id,
        status: 'pending',
        message: 'Image uploaded successfully. Processing started.',
      });
    } catch (error) {
      console.error('Upload error:', error);
      res.status(500).json({ message: 'Upload failed' });
    }
  });

  // Check processing status
  app.get('/api/process/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const processedImage = await storage.getProcessedImage(id);
      
      if (!processedImage) {
        return res.status(404).json({ message: 'Image not found' });
      }

      res.json({
        id: processedImage.id,
        status: processedImage.processingStatus,
        originalPath: processedImage.originalPath,
        processedPath: processedImage.processedPath,
      });
    } catch (error) {
      console.error('Status check error:', error);
      res.status(500).json({ message: 'Failed to check status' });
    }
  });

  // Download processed image
  app.get('/api/download/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const processedImage = await storage.getProcessedImage(id);
      
      if (!processedImage || processedImage.processingStatus !== 'completed') {
        return res.status(404).json({ message: 'Processed image not found' });
      }

      const filePath = path.join('uploads', processedImage.processedPath);
      
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({ message: 'File not found' });
      }

      res.download(filePath, `processed_${processedImage.originalFilename}`);
    } catch (error) {
      console.error('Download error:', error);
      res.status(500).json({ message: 'Download failed' });
    }
  });

  // Newsletter subscription
  app.post('/api/newsletter', async (req, res) => {
    try {
      const validatedData = insertNewsletterSchema.parse(req.body);
      
      // Check if already subscribed
      const existing = await storage.getNewsletterSubscriber(validatedData.email);
      if (existing) {
        if (existing.subscribed) {
          return res.status(400).json({ message: 'Already subscribed' });
        } else {
          // Reactivate subscription
          await storage.updateNewsletterSubscription(validatedData.email, true);
          return res.json({ message: 'Subscription reactivated' });
        }
      }

      await storage.createNewsletterSubscriber(validatedData);
      res.json({ message: 'Successfully subscribed to newsletter' });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: 'Invalid data', errors: error.errors });
      }
      console.error('Newsletter subscription error:', error);
      res.status(500).json({ message: 'Subscription failed' });
    }
  });

  // Unsubscribe from newsletter
  app.post('/api/newsletter/unsubscribe', async (req, res) => {
    try {
      const { email } = req.body;
      
      if (!email) {
        return res.status(400).json({ message: 'Email is required' });
      }

      const updated = await storage.updateNewsletterSubscription(email, false);
      
      if (!updated) {
        return res.status(404).json({ message: 'Email not found' });
      }

      res.json({ message: 'Successfully unsubscribed' });
    } catch (error) {
      console.error('Unsubscribe error:', error);
      res.status(500).json({ message: 'Unsubscribe failed' });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
