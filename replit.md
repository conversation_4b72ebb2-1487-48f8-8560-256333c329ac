# Remove.bg - AI-Powered Background Removal Application

## Overview

This is a full-stack web application that provides AI-powered background removal services. The application allows users to upload images and automatically remove backgrounds using artificial intelligence, similar to the popular remove.bg service. It's built as a modern web application with a React frontend and Express backend, featuring internationalization support, file upload capabilities, and a clean, professional user interface.

## System Architecture

The application follows a monorepo structure with clear separation between client, server, and shared components:

- **Frontend**: React-based SPA using Vite as the build tool
- **Backend**: Express.js REST API server
- **Database**: PostgreSQL with Drizzle ORM
- **UI Framework**: Shadcn/ui components with Tailwind CSS
- **State Management**: TanStack Query for server state management
- **File Storage**: Local file system with configurable paths

## Key Components

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite with custom configuration for development and production
- **Styling**: Tailwind CSS with custom design system variables
- **UI Components**: Shadcn/ui component library based on Radix UI primitives
- **Routing**: Wouter for lightweight client-side routing
- **State Management**: TanStack Query for server state, React hooks for local state
- **Internationalization**: Custom i18n implementation supporting EN, ZH, ES, FR, DE

### Backend Architecture
- **Framework**: Express.js with TypeScript
- **File Upload**: Multer middleware for handling multipart/form-data
- **Database**: PostgreSQL with Drizzle ORM for type-safe database operations
- **Storage**: Local file system with configurable upload directory
- **API Design**: RESTful endpoints following standard conventions

### Database Schema
- **Users**: Basic user management with username/email authentication
- **Processed Images**: Track uploaded and processed images with status tracking
- **Newsletters**: Email subscription management with language preferences

### File Upload System
- **Upload Limits**: 10MB maximum file size
- **File Types**: Images only (validated by MIME type)
- **Storage**: Files stored in local `uploads/` directory
- **Processing**: Asynchronous background removal with status polling
- **Cleanup**: Automatic file deletion after 24 hours (configurable)

## Data Flow

1. **Image Upload**: User selects/drops image → Frontend validates → Multer processes → Stored in uploads directory
2. **Background Processing**: Image queued for AI processing → Status tracked in database → Polling for completion
3. **Result Delivery**: Processed image served via static file serving → Download functionality provided
4. **User Management**: Optional account creation → Image history tracking → Newsletter subscriptions

## External Dependencies

### Core Dependencies
- **Database**: Neon serverless PostgreSQL (@neondatabase/serverless)
- **ORM**: Drizzle ORM with PostgreSQL dialect
- **UI Framework**: Radix UI primitives for accessible components
- **File Processing**: Multer for file uploads
- **Session Management**: connect-pg-simple for PostgreSQL session storage

### Development Dependencies
- **TypeScript**: Full type safety across the stack
- **Vite**: Fast development server and build tool
- **PostCSS**: CSS processing with Tailwind integration
- **ESBuild**: Fast bundling for production server build

### AI Integration
The application is structured to integrate with background removal AI services, though the specific AI provider implementation is abstracted to allow for different services (remove.bg API, local AI models, etc.).

## Deployment Strategy

### Development
- **Hot Reloading**: Vite dev server with HMR for frontend
- **TypeScript Compilation**: Real-time type checking
- **Database Migrations**: Drizzle Kit for schema management
- **Environment Variables**: DATABASE_URL required for PostgreSQL connection

### Production Build
- **Frontend**: Vite builds optimized bundle to `dist/public`
- **Backend**: ESBuild bundles server code to `dist/index.js`
- **Static Files**: Express serves built frontend and uploaded files
- **Database**: Production PostgreSQL instance via connection string

### Deployment Configuration
- **Port**: Configurable via environment variables
- **Database**: PostgreSQL connection via DATABASE_URL
- **File Storage**: Local file system (configurable for cloud storage)
- **Session Storage**: PostgreSQL-backed sessions

## User Preferences

Preferred communication style: Simple, everyday language.

## Recent Changes

- **June 29, 2025 - Real AI Integration**: Successfully integrated remove.bg API for authentic background removal processing. Replaced mock processing with real AI-powered background removal using provided API key.
- **June 29, 2025 - Image Upload Fix**: Fixed critical issue where FormData uploads were failing due to incorrect Content-Type headers being set in apiRequest function. Now properly handles multipart/form-data uploads for image processing.
- **June 29, 2025 - Storage Interface**: Completed storage interface implementation with proper type safety for ProcessedImage and Newsletter schemas.
- **June 29, 2025 - Core Functionality**: All core features now working: image upload, real AI background processing, status polling, and file serving.

## Changelog

- June 29, 2025. Initial setup and complete implementation