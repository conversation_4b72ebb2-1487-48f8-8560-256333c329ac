import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useTranslation, type Language } from "@/lib/i18n";
import { SEOHead } from "@/components/SEOHead";
import { BlogCard } from "@/components/BlogCard";
import { type BlogPost } from "@/lib/types";
import { Search, Calendar, User, Tag, ArrowLeft } from "lucide-react";
import { Link } from "wouter";

interface BlogProps {
  language: Language;
}

export default function Blog({ language }: BlogProps) {
  const { t } = useTranslation(language);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  // Sample blog posts - in a real app, this would come from an API
  const blogPosts: BlogPost[] = [
    {
      id: '1',
      title: 'Improve your editing workflow with Retouch4me and remove.bg',
      excerpt: 'Learn how to combine powerful retouching tools with AI background removal for professional results that save time and deliver stunning quality.',
      date: 'May 09, 2025',
      image: 'https://images.unsplash.com/photo-1616469829581-73993eb86b02?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300',
      slug: 'improve-editing-workflow-retouch4me',
    },
    {
      id: '2',
      title: 'Boost sales with AI-optimized images',
      excerpt: 'Discover how clean, professional product images with removed backgrounds can increase your e-commerce conversion rates by up to 40%.',
      date: 'Feb 12, 2025',
      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300',
      slug: 'boost-sales-ai-optimized-images',
    },
    {
      id: '3',
      title: 'How to make a background transparent in Paint and Paint 3D',
      excerpt: 'Step-by-step guide for creating transparent backgrounds using Microsoft Paint and Paint 3D, including tips for best results.',
      date: 'Feb 10, 2025',
      image: 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300',
      slug: 'transparent-background-paint-paint3d',
    },
    {
      id: '4',
      title: 'Portrait photography: Advanced background removal techniques',
      excerpt: 'Master the art of portrait background removal with advanced techniques for handling complex hair, lighting, and edge cases.',
      date: 'Jan 28, 2025',
      image: 'https://images.unsplash.com/photo-1554080353-a576cf803bda?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300',
      slug: 'portrait-photography-background-removal',
    },
    {
      id: '5',
      title: 'API integration guide: Building automated workflows',
      excerpt: 'Learn how to integrate the remove.bg API into your applications for automated background removal at scale.',
      date: 'Jan 15, 2025',
      image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300',
      slug: 'api-integration-guide-automated-workflows',
    },
    {
      id: '6',
      title: 'Social media marketing with transparent images',
      excerpt: 'Create engaging social media content using transparent background images and discover the best practices for different platforms.',
      date: 'Jan 02, 2025',
      image: 'https://images.unsplash.com/photo-1611162617474-5b21e879e113?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300',
      slug: 'social-media-marketing-transparent-images',
    },
  ];

  const categories = [
    { id: 'all', name: 'All Posts', count: blogPosts.length },
    { id: 'tutorials', name: 'Tutorials', count: 3 },
    { id: 'business', name: 'Business', count: 2 },
    { id: 'technical', name: 'Technical', count: 2 },
  ];

  const featuredPost = blogPosts[0];
  const regularPosts = blogPosts.slice(1);

  const filteredPosts = regularPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesSearch;
  });

  return (
    <>
      <SEOHead
        title="Blog - Tips, Tutorials & Industry Insights | Remove.bg"
        description="Discover expert tips, tutorials, and industry insights on background removal, photo editing, and AI-powered image processing."
        language={language}
      />

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Back Button */}
            <div className="mb-8">
              <Link href="/">
                <Button variant="ghost" className="gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  返回首页
                </Button>
              </Link>
            </div>
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-6xl font-bold mb-6 gradient-text">
                {t("blog.title")}
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t("blog.description")}
              </p>
            </div>

            {/* Search and Filters */}
            <div className="max-w-2xl mx-auto mb-16">
              <div className="relative mb-8">
                <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search articles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 py-3 text-lg"
                />
              </div>

              {/* Categories */}
              <div className="flex flex-wrap justify-center gap-2">
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`${
                      selectedCategory === category.id
                        ? 'gradient-bg text-white'
                        : 'text-gray-600 hover:text-purple-600'
                    }`}
                  >
                    {category.name}
                    <Badge variant="secondary" className="ml-2">
                      {category.count}
                    </Badge>
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Featured Article */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="mb-8">
              <h2 className="text-3xl font-bold mb-4">Featured Article</h2>
            </div>

            <Card className="shadow-xl overflow-hidden">
              <div className="grid lg:grid-cols-2 gap-0">
                <div className="relative h-64 lg:h-auto">
                  <img
                    src={featuredPost.image}
                    alt={featuredPost.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge className="gradient-bg text-white">Featured</Badge>
                  </div>
                </div>
                <CardContent className="p-8 lg:p-12 flex flex-col justify-center">
                  <div className="flex items-center text-sm text-gray-600 mb-4">
                    <Calendar className="h-4 w-4 mr-2" />
                    {featuredPost.date}
                  </div>
                  <h3 className="text-2xl lg:text-3xl font-bold mb-4 text-gray-900 hover:text-purple-600 transition-colors">
                    <a href={`/blog/${featuredPost.slug}`}>
                      {featuredPost.title}
                    </a>
                  </h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {featuredPost.excerpt}
                  </p>
                  <Button className="gradient-bg text-white font-semibold px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1 self-start">
                    Read Article
                  </Button>
                </CardContent>
              </div>
            </Card>
          </div>
        </section>

        {/* Blog Posts Grid */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="mb-8 flex justify-between items-center">
              <h2 className="text-3xl font-bold">Latest Articles</h2>
              <p className="text-gray-600">
                {filteredPosts.length} article{filteredPosts.length !== 1 ? 's' : ''}
              </p>
            </div>

            {filteredPosts.length > 0 ? (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredPosts.map((post) => (
                  <BlogCard key={post.id} post={post} />
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="gradient-bg rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Search className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">No articles found</h3>
                <p className="text-gray-600 mb-6">
                  Try adjusting your search terms or browse all categories.
                </p>
                <Button
                  onClick={() => {
                    setSearchQuery("");
                    setSelectedCategory("all");
                  }}
                  variant="outline"
                >
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        </section>

        {/* Newsletter CTA */}
        <section className="py-16 lg:py-24 bg-gray-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Stay Updated
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Get the latest articles, tutorials, and tips delivered to your inbox. Never miss an update.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Enter your email"
                className="bg-white text-gray-900 border-0"
              />
              <Button className="gradient-bg text-white font-semibold px-8 py-3 rounded-xl hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1">
                Subscribe
              </Button>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
