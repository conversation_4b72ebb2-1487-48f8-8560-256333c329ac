import { useTranslation, type Language } from "@/lib/i18n";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, Settings, Shield, Eye, ArrowLeft } from "lucide-react";
import { <PERSON> } from "wouter";

interface CookiesProps {
  language: Language;
}

export default function Cookies({ language }: CookiesProps) {
  const { t } = useTranslation(language);

  return (
    <div className="min-h-screen bg-gray-50 py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              返回首页
            </Button>
          </Link>
        </div>
        {/* Header */}
        <div className="text-center mb-16">
          <div className="gradient-bg rounded-lg w-16 h-16 flex items-center justify-center mx-auto mb-6">
            <Cookie className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Cookie 政策
          </h1>
          <p className="text-xl text-gray-600">
            了解我们如何使用 Cookie 来改善您的体验
          </p>
          <p className="text-sm text-gray-500 mt-2">
            最后更新：2025年6月29日
          </p>
        </div>

        <div className="space-y-8">
          {/* What are Cookies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-6 w-6 text-purple-600" />
                <span>什么是 Cookie？</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose max-w-none">
              <p>
                Cookie 是在您访问网站时存储在您设备上的小型文本文件。它们被广泛用于使网站工作，
                或更高效地工作，以及向网站所有者提供信息。
              </p>
            </CardContent>
          </Card>

          {/* How we use Cookies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-6 w-6 text-purple-600" />
                <span>我们如何使用 Cookie</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">必要的 Cookie</h3>
                <p className="text-gray-600">
                  这些 Cookie 对于网站的运行是必需的，无法在我们的系统中关闭。
                  它们通常只是为了响应您的操作而设置的。
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">性能 Cookie</h3>
                <p className="text-gray-600">
                  这些 Cookie 允许我们计算访问量和流量来源，以便我们能够测量和改善网站的性能。
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">功能性 Cookie</h3>
                <p className="text-gray-600">
                  这些 Cookie 使网站能够提供增强的功能和个性化。它们可能由我们或第三方提供商设置。
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Cookie Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-6 w-6 text-purple-600" />
                <span>管理 Cookie</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                您可以控制和/或删除 Cookie。有关详细信息，请参阅 aboutcookies.org。
                您可以删除计算机上已有的所有 Cookie，并且可以设置大多数浏览器阻止放置 Cookie。
              </p>
              <div>
                <h3 className="font-semibold mb-2">浏览器设置</h3>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  <li>Chrome：设置 {'>'} 高级 {'>'} 隐私设置和安全性 {'>'} 网站设置 {'>'} Cookie</li>
                  <li>Firefox：首选项 {'>'} 隐私与安全 {'>'} Cookie 和网站数据</li>
                  <li>Safari：首选项 {'>'} 隐私 {'>'} 管理网站数据</li>
                  <li>Edge：设置 {'>'} 网站权限 {'>'} Cookie 和网站数据</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Contact */}
          <Card>
            <CardContent className="p-6 text-center">
              <h3 className="text-lg font-semibold mb-4">有问题？</h3>
              <p className="text-gray-600 mb-4">
                如果您对我们的 Cookie 政策有任何疑问，请随时联系我们。
              </p>
              <p className="text-sm text-gray-500">
                电子邮件：<EMAIL>
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}